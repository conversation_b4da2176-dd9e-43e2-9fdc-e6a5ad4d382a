<?php
/**
 * English Language File
 *
 * This file contains all the text strings used in the application in English.
 */

return [
    // General
    'app_name' => 'Medical Device Management System',
    'dashboard' => 'Dashboard',
    'welcome' => 'Welcome',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'view' => 'View',
    'search' => 'Search',
    'back' => 'Back',
    'actions' => 'Actions',
    'confirm' => 'Confirm',
    'submit' => 'Submit',
    'reset' => 'Reset',
    'yes' => 'Yes',
    'no' => 'No',
    'success' => 'Success',
    'error' => 'Error',
    'warning' => 'Warning',
    'info' => 'Information',
    'required' => 'Required',
    'optional' => 'Optional',
    'status' => 'Status',
    'date' => 'Date',
    'time' => 'Time',
    'description' => 'Description',
    'notes' => 'Notes',
    'details' => 'Details',
    'name' => 'Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'address' => 'Address',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',

    // Authentication
    'login' => 'Login',
    'logout' => 'Logout',
    'username' => 'Username',
    'password' => 'Password',
    'remember_me' => 'Remember Me',
    'forgot_password' => 'Forgot Password?',
    'reset_password' => 'Reset Password',
    'change_password' => 'Change Password',
    'current_password' => 'Current Password',
    'new_password' => 'New Password',
    'confirm_password' => 'Confirm Password',
    'login_failed' => 'Invalid username or password',
    'account_locked' => 'Your account has been locked',
    'password_reset_sent' => 'Password reset instructions have been sent to your email',
    'password_changed' => 'Your password has been changed successfully',

    // User Management
    'users' => 'Users',
    'user' => 'User',
    'add_user' => 'Add User',
    'edit_user' => 'Edit User',
    'delete_user' => 'Delete User',
    'user_details' => 'User Details',
    'full_name' => 'Full Name',
    'role' => 'Role',
    'admin' => 'Administrator',
    'engineer' => 'Engineer',
    'technician' => 'Technician',
    'staff' => 'Staff',
    'permissions' => 'Permissions',
    'language' => 'Language',
    'english' => 'English',
    'arabic' => 'Arabic',

    // Hospital Management
    'hospitals' => 'Hospitals',
    'hospital' => 'Hospital',
    'add_hospital' => 'Add Hospital',
    'edit_hospital' => 'Edit Hospital',
    'delete_hospital' => 'Delete Hospital',
    'hospital_details' => 'Hospital Details',

    // Department Management
    'departments' => 'Departments',
    'department' => 'Department',
    'add_department' => 'Add Department',
    'edit_department' => 'Edit Department',
    'delete_department' => 'Delete Department',
    'department_details' => 'Department Details',

    // Device Management
    'devices' => 'Medical Devices',
    'device' => 'Medical Device',
    'add_device' => 'Add Device',
    'edit_device' => 'Edit Device',
    'delete_device' => 'Delete Device',
    'device_details' => 'Device Details',
    'model' => 'Model',
    'serial_number' => 'Serial Number',
    'manufacturer' => 'Manufacturer',
    'purchase_date' => 'Purchase Date',
    'warranty_expiry' => 'Warranty Expiry',
    'device_status' => 'Device Status',
    'operational' => 'Operational',
    'under_maintenance' => 'Under Maintenance',
    'out_of_order' => 'Out of Order',
    'retired' => 'Retired',
    'qr_code' => 'QR Code',
    'generate_qr' => 'Generate QR Code',
    'print_qr' => 'Print QR Code',

    // Maintenance Management
    'maintenance' => 'Maintenance',
    'maintenance_schedules' => 'Maintenance Schedules',
    'maintenance_logs' => 'Maintenance Logs',
    'add_schedule' => 'Add Schedule',
    'edit_schedule' => 'Edit Schedule',
    'delete_schedule' => 'Delete Schedule',
    'schedule_details' => 'Schedule Details',
    'add_log' => 'Add Log',
    'edit_log' => 'Edit Log',
    'delete_log' => 'Delete Log',
    'log_details' => 'Log Details',
    'scheduled_date' => 'Scheduled Date',
    'performed_date' => 'Performed Date',
    'performed_by' => 'Performed By',
    'frequency' => 'Frequency',
    'daily' => 'Daily',
    'weekly' => 'Weekly',
    'monthly' => 'Monthly',
    'quarterly' => 'Quarterly',
    'yearly' => 'Yearly',
    'maintenance_status' => 'Maintenance Status',
    'scheduled' => 'Scheduled',
    'completed' => 'Completed',
    'overdue' => 'Overdue',
    'cancelled' => 'Cancelled',

    // Ticket System
    'tickets' => 'Tickets',
    'ticket' => 'Ticket',
    'add_ticket' => 'Add Ticket',
    'edit_ticket' => 'Edit Ticket',
    'delete_ticket' => 'Delete Ticket',
    'ticket_details' => 'Ticket Details',
    'reported_by' => 'Reported By',
    'assigned_to' => 'Assigned To',
    'title' => 'Title',
    'priority' => 'Priority',
    'low' => 'Low',
    'medium' => 'Medium',
    'high' => 'High',
    'critical' => 'Critical',
    'ticket_status' => 'Ticket Status',
    'open' => 'Open',
    'in_progress' => 'In Progress',
    'resolved' => 'Resolved',
    'closed' => 'Closed',
    'ticket_updates' => 'Ticket Updates',
    'add_comment' => 'Add Comment',
    'comment' => 'Comment',

    // Notifications
    'notifications' => 'Notifications',
    'notification' => 'Notification',
    'mark_as_read' => 'Mark as Read',
    'mark_all_as_read' => 'Mark All as Read',
    'no_notifications' => 'No Notifications',

    // Reports and Statistics
    'reports' => 'Reports',
    'statistics' => 'Statistics',
    'device_status_report' => 'Device Status Report',
    'maintenance_compliance_report' => 'Maintenance Compliance Report',
    'ticket_resolution_report' => 'Ticket Resolution Report',
    'generate_report' => 'Generate Report',
    'export_pdf' => 'Export to PDF',
    'export_excel' => 'Export to Excel',
    'from_date' => 'From Date',
    'to_date' => 'To Date',

    // Error Messages
    'access_denied' => 'Access Denied',
    'not_found' => 'Not Found',
    'server_error' => 'Server Error',
    'form_errors' => 'There are errors in your form submission',
    'required_field' => 'This field is required',
    'invalid_email' => 'Please enter a valid email address',
    'passwords_not_match' => 'Passwords do not match',
    'file_too_large' => 'File is too large',
    'invalid_file_type' => 'Invalid file type',

    // Success Messages
    'created_successfully' => '{0} created successfully',
    'updated_successfully' => '{0} updated successfully',
    'deleted_successfully' => '{0} deleted successfully',

    // UI/UX
    'toggle_dark_mode' => 'Toggle Dark Mode',
    'dark_mode' => 'Dark Mode',
    'light_mode' => 'Light Mode',
    'export_options' => 'Export Options',
    'export_format' => 'Export Format',
    'export' => 'Export',
    'filter' => 'Filter',
    'filter_by_hospital' => 'Filter by Hospital',
    'all_hospitals' => 'All Hospitals',
    'saving' => 'Saving',
    'loading' => 'Loading',
    'processing' => 'Processing',
    'view_details' => 'View Details',
    'back_to_list' => 'Back to List',

    // Device specific
    'device_name' => 'Device Name',
    'device_details' => 'Device Details',
    'device_information' => 'Device Information',
    'devices_list' => 'Devices List',
    'add_device' => 'Add Device',
    'create_device' => 'Create Device',
    'edit_device' => 'Edit Device',
    'update_device' => 'Update Device',
    'delete_device_confirm' => 'Are you sure you want to delete this device?',
    'no_devices' => 'No devices found',
    'all_devices' => 'All Devices',
    'all_statuses' => 'All Statuses',
    'all_departments' => 'All Departments',
    'search_devices' => 'Search devices...',
    'device_url' => 'Device URL',
    'qr_code_description' => 'Scan this QR code to quickly access device information',
    'qr_code_url_description' => 'This URL can be accessed by scanning the QR code',
    'no_qr_code_generated' => 'No QR code has been generated for this device',
    'generate_qr_code' => 'Generate QR Code',
    'regenerate_qr' => 'Regenerate QR Code',
    'regenerate_qr_code' => 'Regenerate QR Code',
    'regenerate_qr_confirm' => 'Are you sure you want to regenerate the QR code?',
    'download_qr' => 'Download QR Code',
    'print_qr' => 'Print QR Code',
    'copy_failed' => 'Failed to copy URL to clipboard',
    'warranty_expired' => 'Warranty Expired',
    'years_old' => 'Years Old',
    'maintenance_records' => 'Maintenance Records',
    'no_maintenance_history' => 'No maintenance history found',
    'quick_actions' => 'Quick Actions',
    'schedule_maintenance' => 'Schedule Maintenance',
    'report_issue' => 'Report Issue',
    'statistics' => 'Statistics',
    'device_qr_code' => 'Device QR Code',
    'no_qr_code' => 'No QR code available',
    'generate_qr' => 'Generate QR Code',
    'select_category' => 'Select Category',
    'cardiology' => 'Cardiology',
    'radiology' => 'Radiology',
    'emergency' => 'Emergency',
    'surgery' => 'Surgery',
    'laboratory' => 'Laboratory',
    'other' => 'Other',
    'maintenance_interval' => 'Maintenance Interval',
    'days' => 'Days',

    // Password Reset
    'reset_password' => 'Reset Password',
    'send_reset_link' => 'Send Reset Link',
    'reset_email_sent' => 'If an account with that email exists, a password reset link has been sent',
    'reset_email_help' => 'Enter your email address and we will send you a link to reset your password',
    'new_password' => 'New Password',
    'confirm_password' => 'Confirm Password',
    'password_requirements' => 'Password must be at least 8 characters long',
    'passwords_not_match' => 'Passwords do not match',
    'password_too_short' => 'Password must be at least 8 characters long',
    'password_reset_success' => 'Your password has been reset successfully',
    'password_reset_failed' => 'Failed to reset password. Please try again',
    'invalid_reset_token' => 'Invalid or expired reset token',
    'password_reset_complete' => 'Password Reset Complete',
    'password_reset_complete_message' => 'Your password has been successfully reset. You can now log in with your new password',
    'back_to_login' => 'Back to Login',
    'password_reset_subject' => 'Password Reset Request',
    'password_reset_message' => 'You have requested to reset your password. Click the button below to reset it',
    'password_reset_expire' => 'This link will expire in 1 hour',
    'password_reset_ignore' => 'If you did not request this password reset, please ignore this email',
    'hello' => 'Hello',

    // CSRF and Security
    'invalid_csrf_token' => 'Invalid security token. Please try again',
    'email_send_failed' => 'Failed to send email. Please contact administrator',

    // Maintenance
    'maintenance_schedules' => 'Maintenance Schedules',
    'maintenance_logs' => 'Maintenance Logs',
    'no_maintenance_schedules' => 'No maintenance schedules found',
    'scheduled_date' => 'Scheduled Date',
    'frequency' => 'Frequency',
    'once' => 'Once',
    'daily' => 'Daily',
    'weekly' => 'Weekly',
    'monthly' => 'Monthly',
    'quarterly' => 'Quarterly',
    'yearly' => 'Yearly',
    'delete_schedule_confirm' => 'Are you sure you want to delete this maintenance schedule?',
    'filter' => 'Filter',
    'created_by' => 'Created By',

    // Tickets
    'tickets' => 'Tickets',
    'create_ticket' => 'Create Ticket',
    'ticket_id' => 'Ticket ID',
    'no_tickets' => 'No tickets found',
    'all_priorities' => 'All Priorities',
    'search_tickets' => 'Search tickets...',
    'urgent' => 'Urgent',
    'open' => 'Open',
    'in_progress' => 'In Progress',
    'resolved' => 'Resolved',
    'closed' => 'Closed',
    'no_device' => 'No Device',
    'delete_ticket_confirm' => 'Are you sure you want to delete this ticket?',
    'select_device_optional' => 'Select Device (Optional)',
    'device_optional_help' => 'Select a device if this ticket is related to a specific device',
    'technical' => 'Technical',
    'repair' => 'Repair',
    'calibration' => 'Calibration',
    'training' => 'Training',
    'description_help' => 'Provide detailed information about the issue or request',
    'attachments' => 'Attachments',
    'attachments_help' => 'Upload images, documents or other files related to this ticket (Max 10MB per file)',
    'urgent_notification' => 'Send urgent notification',
    'urgent_notification_help' => 'Check this box to send immediate notifications to administrators',
    'file_too_large' => 'File is too large',
    'file_type_not_allowed' => 'File type not allowed',

    // Users
    'users' => 'Users',
    'add_user' => 'Add User',
    'no_users' => 'No users found',
    'all_roles' => 'All Roles',
    'search_users' => 'Search users...',
    'admin' => 'Admin',
    'manager' => 'Manager',
    'technician' => 'Technician',
    'user' => 'User',
    'no_hospital' => 'No Hospital',
    'never' => 'Never',
    'delete_user_confirm' => 'Are you sure you want to delete this user?',
    'confirm_status_change' => 'Confirm Status Change',
    'confirm_status_change_message' => 'Are you sure you want to %action% this user?',
    'deactivate' => 'deactivate',
    'activate' => 'activate',
    'confirm' => 'Confirm',

    // Reports
    'reports' => 'Reports',
    'generate_report' => 'Generate Report',
    'date_from' => 'Date From',
    'date_to' => 'Date To',
    'total_devices' => 'Total Devices',
    'maintenance_due' => 'Maintenance Due',
    'open_tickets' => 'Open Tickets',
    'warranty_expiring' => 'Warranty Expiring',
    'devices_by_status' => 'Devices by Status',
    'maintenance_trends' => 'Maintenance Trends',
    'recent_maintenance' => 'Recent Maintenance',
    'urgent_tickets' => 'Urgent Tickets',
    'no_recent_maintenance' => 'No recent maintenance',
    'no_urgent_tickets' => 'No urgent tickets',
    'maintenance_completed' => 'Maintenance Completed',

    // Notifications
    'notifications' => 'Notifications',
    'mark_all_read' => 'Mark All Read',
    'clear_all' => 'Clear All',
    'all_types' => 'All Types',
    'all_notifications' => 'All Notifications',
    'unread' => 'Unread',
    'read' => 'Read',
    'no_notifications' => 'No Notifications',
    'no_notifications_message' => 'You have no notifications at this time.',
    'new' => 'New',
    'mark_as_read' => 'Mark as Read',
    'mark_as_unread' => 'Mark as Unread',
    'delete_notification_confirm' => 'Are you sure you want to delete this notification?',
    'mark_all_read_confirm' => 'Are you sure you want to mark all notifications as read?',
    'clear_all_notifications_confirm' => 'Are you sure you want to clear all notifications? This action cannot be undone.',
    'error_occurred' => 'An error occurred. Please try again.',
    'system' => 'System',
    'warning' => 'Warning',
    'info' => 'Info',

    // Enhanced UI strings
    'welcome_back' => 'Welcome Back',
    'manage_your_medical_devices' => 'Manage your medical devices efficiently',
    'overview_of_your_medical_devices' => 'Overview of your medical devices and system status',
    'dashboard_welcome_message' => 'Here\'s what\'s happening with your medical devices today.',
    'secure_login_portal' => 'Secure login portal for healthcare professionals',
    'enter_username' => 'Enter your username',
    'enter_password' => 'Enter your password',
    'devices_management' => 'Devices Management',
    'manage_all_medical_devices' => 'Manage all medical devices in your facility',
    'of_total' => 'of total',
    'needs_attention' => 'Needs attention',
    'requires_repair' => 'Requires repair',
    'devices_found' => 'devices found',
    'clear_filters' => 'Clear Filters',
    'filters' => 'Filters',
    'create_new_hospital_facility' => 'Create a new hospital facility',
    'update_hospital_information' => 'Update hospital information',
    'hospital_information' => 'Hospital Information',
    'register_new_medical_device' => 'Register a new medical device',
    'device_information' => 'Device Information',
    'create_new_user_account' => 'Create a new user account',
    'update_user_information' => 'Update user information',
    'user_information' => 'User Information',
    'leave_blank_to_keep_current' => 'Leave blank to keep current password',
    'leave_blank_to_keep_current_password' => 'Leave blank to keep current password',
    'create_user' => 'Create User',
    'update_user' => 'Update User',
    'report_new_issue_or_request' => 'Report a new issue or request support',
    'ticket_information' => 'Ticket Information',
    'update_ticket' => 'Update Ticket',
    'schedule_new_maintenance_task' => 'Schedule a new maintenance task',
    'schedule_information' => 'Schedule Information',
    'maintenance_description_help' => 'Describe the maintenance tasks to be performed',
    'update_schedule' => 'Update Schedule',
    'select_role' => 'Select Role',
    'select_hospital' => 'Select Hospital',
    'select_priority' => 'Select Priority',
    'select_status' => 'Select Status',
    'select_category' => 'Select Category',
    'select_frequency' => 'Select Frequency',
    'unassigned' => 'Unassigned',
    'active' => 'Active',
    'inactive' => 'Inactive',
];
